* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    color: #333;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    text-align: center;
    max-width: 500px;
    width: 100%;
}

.game-header h1 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.score-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 1.2em;
    font-weight: bold;
    color: #2d3748;
}

.score-container span {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.game-area {
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

#gameCanvas {
    border: 3px solid #2d3748;
    border-radius: 10px;
    background: #1a202c;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
}

.game-controls {
    margin: 20px 0;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.game-controls button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.game-controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.game-controls button:active {
    transform: translateY(0);
}

.game-controls button:disabled {
    background: #a0aec0;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.instructions {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #667eea;
}

.instructions h3 {
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 1.3em;
}

.instructions p {
    color: #4a5568;
    margin-bottom: 5px;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .game-container {
        margin: 20px;
        padding: 20px;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 350px;
        height: auto;
    }
    
    .score-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .game-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .game-controls button {
        width: 100%;
        max-width: 200px;
    }
}

/* 游戏结束弹窗样式 */
.game-over-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.game-over-popup {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: popup 0.3s ease-out;
}

@keyframes popup {
    from {
        transform: scale(0.7);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.game-over-popup h2 {
    color: #e53e3e;
    margin-bottom: 20px;
    font-size: 2em;
}

.game-over-popup p {
    color: #4a5568;
    margin-bottom: 30px;
    font-size: 1.2em;
}

.game-over-popup button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.game-over-popup button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}
