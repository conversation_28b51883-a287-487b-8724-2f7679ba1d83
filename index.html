<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>贪吃蛇游戏</h1>
            <div class="score-container">
                <span>得分: <span id="score">0</span></span>
                <span>最高分: <span id="highScore">0</span></span>
            </div>
        </div>
        
        <div class="game-area">
            <canvas id="gameCanvas" width="400" height="400"></canvas>
        </div>
        
        <div class="game-controls">
            <button id="startBtn">开始游戏</button>
            <button id="pauseBtn" disabled>暂停</button>
            <button id="resetBtn">重新开始</button>
        </div>
        
        <div class="instructions">
            <h3>游戏说明：</h3>
            <p>使用方向键 ↑↓←→ 或 WASD 键控制蛇的移动</p>
            <p>吃到食物可以增加长度和得分</p>
            <p>避免撞到墙壁或自己的身体</p>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
